{"cells": [{"cell_type": "markdown", "metadata": {"id": "0aStgWSO0E0E"}, "source": ["# **03. Exploratory  Data Analysis**\n", "*This notebook will contain the exploratory data analysis (EDA), including visualizing data distributions, relationships between variables, and identifying patterns.*"]}, {"cell_type": "markdown", "metadata": {"id": "1eLEkw5O0ECa"}, "source": ["## Objectives\n", "\n", "- Perform exploratory data analysis (EDA) on the bulldozer price dataset to understand the data distribution, identify patterns, and detect any anomalies or missing values.\n", "\n", "## Inputs\n", "\n", "- The bulldozer price dataset (CSV file) containing historical auction prices and related features.\n", "\n", "## Outputs\n", "\n", "- Visualizations such as histograms, scatter plots, and box plots to illustrate data distributions and relationships.\n", "- Summary statistics and insights derived from the EDA process.\n", " \n", "\n", "## Additional Comments\n", "\n", "- Ensure that all necessary libraries (e.g., pandas, matplotlib, seaborn) are installed before running the notebook.\n", "- Document any assumptions or decisions made during the EDA process for future reference.\n", "- This notebook is a crucial step in our machine learning pipeline, exploratory data analysis (EDA) with traditional data analysis and machine learning techniques.\n", "##### **Traditional Data Analysis Techniques**\n", "1. **Data Cleaning:**\n", "    - Handling missing values (e.g., filling with mean/median, dropping rows/columns).\n", "    - Removing duplicates.\n", "    - Correcting data types.\n", "2. **Exploratory Data Analysis (EDA):**\n", "    - Descriptive Statistics: Calculating mean, median, mode, standard deviation, etc.\n", "    - Data Visualization: Using plots such as histograms, scatter plots, box plots, and correlation matrices to understand data distributions and relationships.\n", "    - Feature Engineering: Creating new features from existing ones to improve model performance.\n", "3. **Data Transformation:**\n", "    - Normalization or standardization of features.\n", "    - Encoding categorical variables (e.g., one-hot encoding).\n", "\n", "##### **Machine Learning Techniques**\n", "1. **Data Splitting:**\n", "    - Dividing the dataset into training and testing sets to evaluate model performance.\n", "2. **Model Selection:**\n", "    - Choosing appropriate machine learning algorithms (e.g., linear regression, decision trees, random forests, etc.).\n", "3. **Model Training:**\n", "    - Fitting the chosen model to the training data.\n", "4.  **Model Evaluation:**\n", "    - Using metrics such as accuracy, precision, recall, F1-score, and ROC-AUC to assess model performance on the test set.\n", "5.  **Hyperparameter Tuning:**\n", "    - Optimizing model parameters using techniques like grid search or random search.\n", "6.  **Model Interpretation:**\n", "    - Analyzing feature importance or coefficients to understand the model's decision-making process.\n"]}, {"cell_type": "markdown", "metadata": {"id": "9uWZXH9LwoQg"}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Execution Timestamp\n", "\n", "Purpose: This code block adds a timestamp to track notebook execution\n", "- Helps monitor when analysis was last performed\n", "- Ensures reproducibility of results\n", "- Useful for debugging and version control"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Notebook last run (end-to-end): 2025-03-24 10:32:06.327339\n"]}], "source": ["# Timestamp\n", "import datetime\n", "\n", "import datetime\n", "print(f\"Notebook last run (end-to-end): {datetime.datetime.now()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Project Directory Structure and Working Directory\n", "\n", "**Purpose: This code block establishes and explains the project organization**\n", "- Creates a standardized project structure for data science workflows\n", "- Documents the purpose of each directory for team collaboration\n", "- Gets current working directory for file path management\n", "\n", "## Key Components:\n", "1. `data/ directory` stores all datasets (raw, processed, interim)\n", "2. `src/` contains all source code (data preparation, models, utilities)\n", "3. `notebooks/` holds <PERSON><PERSON><PERSON> notebooks for experimentation\n", "4. `results/` stores output files and visualizations\n", "\n", "## Project Root Structure\n", "\n", "- **`data/`** - Where all your datasets live\n", "    - `raw/` - Original, untouched data\n", "    - `processed/` - Cleaned and prepared data\n", "    - `interim/` - Temporary data files\n", "- **`src/`** - Your source code\n", "    - `data_prep/` - Code for preparing data\n", "    - `models/` - Your ML models\n", "    - `utils/` - Helper functions\n", "- **`notebooks/`** - <PERSON><PERSON><PERSON> notebooks for experiments\n", "- **`results/`** - Model outputs and visualizations"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting Up Working Directory\n", "This code block sets up the working environment by:\n", "- Changing to the project directory where our code and data files are located\n", "- Verifying the current working directory to ensure we're in the right place"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["'c:\\\\Users\\\\<USER>\\\\Dropbox\\\\1 PROJECT\\\\VS Code Project Respository\\\\About-BulldozerPriceGenius-_BPG-_v2'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "\n", "# Move to the desired directory\n", "os.chdir('c:\\\\Users\\\\<USER>\\\\Dropbox\\\\1 PROJECT\\\\VS Code Project Respository\\\\About-BulldozerPriceGenius-_BPG-_v2')\n", "\n", "# Get the current directory to verify the change\n", "current_dir = os.getcwd()\n", "current_dir"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Set Working Directory to Project Root\n", "**Purpose: Changes the current working directory to the parent directory**\n", "- Gets the folder one level above the current one\n", "- Makes sure all file locations work correctly throughout the project\n", "- Keeps files and folders organized in a clean way"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You set a new current directory\n"]}], "source": ["os.chdir(os.path.dirname(current_dir))\n", "print(\"You set a new current directory\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Get Current Working Directory\n", "**Purpose: Retrieves and stores the current working directory path**\n", "- Gets the folder location where we're currently working\n", "- Saves this location in a variable called current_dir so we can use it later\n", "- Helps us find and work with files in the right place"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["'c:\\\\Users\\\\<USER>\\\\Dropbox\\\\1 PROJECT\\\\VS Code Project Respository'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "\n", "# Change the current working directory\n", "os.chdir('c:\\\\Users\\\\<USER>\\\\Dropbox\\\\1 PROJECT\\\\VS Code Project Respository')\n", "\n", "# Get the current working directory\n", "current_dir = os.getcwd()\n", "current_dir"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# **Import Essential Data Science Libraries and Check Versions**\n", "\n", "**Purpose: This code block imports fundamental Python libraries for data analysis and visualization**\n", "- `pandas:` For data manipulation and analysis\n", "- `numpy:` For numerical computations\n", "- `matplotlib:` For creating visualizations and plots\n", "\n", "**The version checks help ensure:**\n", "- *Code compatibility across different environments*\n", "- *Reproducibility of analysis*\n", "- *Easy debugging of version-specific issues*\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["pandas version: 2.2.3\n", "NumPy version: 2.2.4\n", "matplotlib version: 3.10.1\n"]}], "source": ["# Import data analysis tools\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "print(f\"pandas version: {pd.__version__}\")\n", "print(f\"NumPy version: {np.__version__}\")\n", "print(f\"matplotlib version: {matplotlib.__version__}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# **Model Driven Data Exploration**\n", "## Early Modeling Approach\n", "\n", "After preparing our data, we'll take a unique approach by starting with modeling early in our analysis. Here's why this makes sense:\n", "\n", "- We already know our target metric (RMSLE)\n", "- Using a model can help us understand our data better while working towards our goal\n", "- This approach helps us get faster results\n", "\n", "Based on our dataset size (over 100,000 examples), we'll use either a SGD Regressor or Random Forest model. We'll start with Random Forest since we're familiar with it"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Initial Random Forest Model Attempt\n", "\n", "This code demonstrates an initial attempt to train a [Random Forest model](https://scikit-learn.org/stable/modules/generated/sklearn.ensemble.RandomForestRegressor.html) on our dataset. However, it will fail due to two key data quality issues:\n", "\n", "- Missing numerical values that need to be handled\n", "- Categorical variables that need to be encoded"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# This won't work since we've got missing numbers and categories\n", "# from sklearn.ensemble import RandomForestRegressor\n", "\n", "# model = RandomForestRegressor(n_jobs=-1)\n", "# model.fit(X=df_tmp.drop(\"SalePrice\", axis=1), # use all columns except SalePrice as X input\n", "#          y=df_tmp.SalePrice) # use SalePrice column as y input"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Loading and Initial Data Inspection\n", "\n", "This code snippet performs two essential data preparation tasks:\n", "\n", "- Loads a CSV file that contains bulldozer prices\n", "- Performs initial data inspection by checking:\n", "    - File existence verification\n", "    - Basic dataset information including missing values and data types"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The file 'C:/Users/<USER>/Dropbox/1 PROJECT/VS Code Project Respository/About-BulldozerPriceGenius-_BPG-_v2/data/processed/TrainAndValid_processed.csv' exists.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_2340\\195247768.py:11: DtypeWarning: Columns (12,38,39,40) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df_tmp = pd.read_csv(file_path)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 412698 entries, 0 to 412697\n", "Data columns (total 57 columns):\n", " #   Column                    Non-Null Count   Dtype  \n", "---  ------                    --------------   -----  \n", " 0   SalesID                   412698 non-null  int64  \n", " 1   SalePrice                 412698 non-null  float64\n", " 2   MachineID                 412698 non-null  int64  \n", " 3   ModelID                   412698 non-null  int64  \n", " 4   datasource                412698 non-null  int64  \n", " 5   auctioneerID              392562 non-null  float64\n", " 6   YearMade                  412698 non-null  int64  \n", " 7   MachineHoursCurrentMeter  147504 non-null  float64\n", " 8   UsageBand                 73670 non-null   object \n", " 9   fiModelDesc               412698 non-null  object \n", " 10  fiBaseModel               412698 non-null  object \n", " 11  fiSecondaryDesc           271971 non-null  object \n", " 12  fiModelSeries             58667 non-null   object \n", " 13  fiModelDescriptor         74816 non-null   object \n", " 14  ProductSize               196093 non-null  object \n", " 15  fiProductClassDesc        412698 non-null  object \n", " 16  state                     412698 non-null  object \n", " 17  ProductGroup              412698 non-null  object \n", " 18  ProductGroupDesc          412698 non-null  object \n", " 19  Drive_System              107087 non-null  object \n", " 20  Enclosure                 412364 non-null  object \n", " 21  Forks                     197715 non-null  object \n", " 22  Pad_Type                  81096 non-null   object \n", " 23  Ride_Control              152728 non-null  object \n", " 24  Stick                     81096 non-null   object \n", " 25  Transmission              188007 non-null  object \n", " 26  Turbocharged              81096 non-null   object \n", " 27  Blade_Extension           25983 non-null   object \n", " 28  <PERSON>_Width               25983 non-null   object \n", " 29  Enclosure_Type            25983 non-null   object \n", " 30  Engine_Horsepower         25983 non-null   object \n", " 31  Hydraulics                330133 non-null  object \n", " 32  Pushblock                 25983 non-null   object \n", " 33  <PERSON><PERSON><PERSON>                    106945 non-null  object \n", " 34  Scarifier                 25994 non-null   object \n", " 35  Tip_Control               25983 non-null   object \n", " 36  Tire_Size                 97638 non-null   object \n", " 37  <PERSON>upler                   220679 non-null  object \n", " 38  Coupler_System            44974 non-null   object \n", " 39  Grouser_Tracks            44875 non-null   object \n", " 40  Hydraulics_Flow           44875 non-null   object \n", " 41  Track_Type                102193 non-null  object \n", " 42  Undercarriage_Pad_Width   102916 non-null  object \n", " 43  Stick_Length              102261 non-null  object \n", " 44  Thumb                     102332 non-null  object \n", " 45  Pattern_Changer           102261 non-null  object \n", " 46  Grouser_Type              102193 non-null  object \n", " 47  Backhoe_Mounting          80712 non-null   object \n", " 48  Blade_Type                81875 non-null   object \n", " 49  Travel_Controls           81877 non-null   object \n", " 50  Differential_Type         71564 non-null   object \n", " 51  Steering_Controls         71522 non-null   object \n", " 52  saleYear                  412698 non-null  int64  \n", " 53  saleMonth                 412698 non-null  int64  \n", " 54  saleDay                   412698 non-null  int64  \n", " 55  saleDayofweek             412698 non-null  int64  \n", " 56  saleDayofyear             412698 non-null  int64  \n", "dtypes: float64(3), int64(10), object(44)\n", "memory usage: 179.5+ MB\n"]}], "source": ["import os\n", "import pandas as pd\n", "\n", "# Define the file path\n", "file_path = \"C:/Users/<USER>/Dropbox/1 PROJECT/VS Code Project Respository/About-BulldozerPriceGenius-_BPG-_v2/data/processed/TrainAndValid_processed.csv\"\n", "\n", "# Check if the file exists\n", "if os.path.exists(file_path):\n", "    print(f\"The file '{file_path}' exists.\")\n", "    # Load the CSV file into a DataFrame\n", "    df_tmp = pd.read_csv(file_path)\n", "    # Check for missing values and different datatypes\n", "    df_tmp.info()\n", "else:\n", "    print(f\"The file '{file_path}' does not exist.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Check Missing Values in Sample Data\n", "\n", "This code examines the data for missing values:\n", "\n", "- Uses `.head()` to display first few rows of data\n", "- Applies `.isna()` to identify missing values\n", "- Provides quick initial assessment of data gaps\n", "- Helps spot potential issues before full analysis"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>SalesID</th>\n", "      <th>SalePrice</th>\n", "      <th>MachineID</th>\n", "      <th>ModelID</th>\n", "      <th>datasource</th>\n", "      <th>auctioneerID</th>\n", "      <th>YearMade</th>\n", "      <th>MachineHoursCurrentMeter</th>\n", "      <th>UsageBand</th>\n", "      <th>fiModelDesc</th>\n", "      <th>...</th>\n", "      <th><PERSON><PERSON>_Mounting</th>\n", "      <th>Blade_Type</th>\n", "      <th>Travel_Controls</th>\n", "      <th>Differential_Type</th>\n", "      <th>Steering_Controls</th>\n", "      <th>saleYear</th>\n", "      <th>saleMonth</th>\n", "      <th>saleDay</th>\n", "      <th>saleDayofweek</th>\n", "      <th>saleDayofyear</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 57 columns</p>\n", "</div>"], "text/plain": ["   SalesID  SalePrice  MachineID  ModelID  datasource  auctioneerID  YearMade  \\\n", "0    False      False      False    False       False         False     False   \n", "1    False      False      False    False       False         False     False   \n", "2    False      False      False    False       False         False     False   \n", "3    False      False      False    False       False         False     False   \n", "4    False      False      False    False       False         False     False   \n", "\n", "   MachineHoursCurrentMeter  UsageBand  fiModelDesc  ...  Backhoe_Mounting  \\\n", "0                     False      False        False  ...              True   \n", "1                     False      False        False  ...              True   \n", "2                     False      False        False  ...              True   \n", "3                     False      False        False  ...              True   \n", "4                     False      False        False  ...              True   \n", "\n", "   Blade_Type  Travel_Controls  Differential_Type  Steering_Controls  \\\n", "0        True             True              False              False   \n", "1        True             True              False              False   \n", "2        True             True               True               True   \n", "3        True             True               True               True   \n", "4        True             True               True               True   \n", "\n", "   saleYear  saleMonth  saleDay  saleDayofweek  saleDayofyear  \n", "0     False      False    False          False          False  \n", "1     False      False    False          False          False  \n", "2     False      False    False          False          False  \n", "3     False      False    False          False          False  \n", "4     False      False    False          False          False  \n", "\n", "[5 rows x 57 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# Find missing values in the head of our DataFrame \n", "df_tmp.head().isna()"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Missing Values Analysis\n", "This code analyzes missing data by:\n", "\n", "- Identifying empty or missing values throughout the dataset\n", "- Helping assess data quality\n", "- Providing insights for handling data gaps"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["SalesID                          0\n", "SalePrice                        0\n", "MachineID                        0\n", "ModelID                          0\n", "datasource                       0\n", "auctioneerID                 20136\n", "YearMade                         0\n", "MachineHoursCurrentMeter    265194\n", "UsageBand                   339028\n", "fiModelDesc                      0\n", "fiBaseModel                      0\n", "fiSecondaryDesc             140727\n", "fiModelSeries               354031\n", "fiModelDescriptor           337882\n", "ProductSize                 216605\n", "fiProductClassDesc               0\n", "state                            0\n", "ProductGroup                     0\n", "ProductGroupDesc                 0\n", "Drive_System                305611\n", "Enclosure                      334\n", "Forks                       214983\n", "Pad_Type                    331602\n", "Ride_Control                259970\n", "Stick                       331602\n", "Transmission                224691\n", "Turbocharged                331602\n", "Blade_Extension             386715\n", "Blade_Width                 386715\n", "Enclosure_Type              386715\n", "Engine_Horsepower           386715\n", "Hydraulics                   82565\n", "Pushblock                   386715\n", "Ripper                      305753\n", "Scarifier                   386704\n", "Tip_Control                 386715\n", "Tire_Size                   315060\n", "<PERSON>upler                     192019\n", "Coupler_System              367724\n", "Grouser_Tracks              367823\n", "Hydraulics_Flow             367823\n", "Track_Type                  310505\n", "Undercarriage_Pad_Width     309782\n", "Stick_Length                310437\n", "Thumb                       310366\n", "Pattern_Changer             310437\n", "Grouser_Type                310505\n", "Backhoe_Mounting            331986\n", "Blade_Type                  330823\n", "Travel_Controls             330821\n", "Differential_Type           341134\n", "Steering_Controls           341176\n", "saleYear                         0\n", "saleMonth                        0\n", "saleDay                          0\n", "saleDayofweek                    0\n", "saleDayofyear                    0\n", "dtype: int64"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# Check for total missing values per column\n", "df_tmp.isna().sum()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Check Data Type of UsageBand Column\n", "\n", "This code looks at what kind of data is in the `'UsageBand'` column. This is important to know because:\n", "\n", "- It shows us what type of information is in this column and how it's organized\n", "- It helps us know what kind of math and changes we can do with the data in this column\n", "- It helps us decide how to clean and organize the data for our analysis"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["(dtype('O'), 'object')"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# Get the dtype of a given column\n", "df_tmp[\"UsageBand\"].dtype, df_tmp[\"UsageBand\"].dtype.name"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Check Column Data Type\n", "\n", "This code checks if a specific column contains string (text) data. This is important because:\n", "\n", "- It helps verify data types before processing\n", "- It ensures we handle string data appropriately in our analysis\n", "- It helps prevent errors when applying operations that require specific data type"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# Check whether a column is a string\n", "pd.api.types.is_string_dtype(df_tmp[\"state\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Dictionary Iteration Test\n", "\n", "This code shows how to work with a dictionary's content by going through each of its parts:\n", "\n", "- How to access both keys and values simultaneously\n", "- Proper syntax for dictionary iteration in Python\n", "- String formatting using f-strings to display results"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["This is a key: key1\n", "This is a value: All\n", "\n", "This is a key: key2\n", "This is a value: Good!\n", "\n"]}], "source": ["# Quick exampke of calling .items() on a dictionary\n", "random_dict = {\"key1\": \"All\",\n", "               \"key2\": \"Good!\"}\n", "\n", "for key, value in random_dict.items():\n", "    print(f\"This is a key: {key}\")\n", "    print(f\"This is a value: {value}\")\n", "    print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## String Column Analysis\n", "\n", "This code performs a detailed analysis of string-type columns in our DataFrame:\n", "\n", "- Iterates through each column to identify string data types\n", "- For each string column, it provides:\n", "    - Column name and its data type\n", "    - A random sample value from the column\n", "    - The inferred data type of the sample value\n", "\n", "This step helps us look at our text data more closely so we can get it ready for analysis."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Column name: fiModelDesc | Column dtype: object | Example value: ['120'] | Example value dtype: string\n", "Column name: fiBaseModel | Column dtype: object | Example value: ['PC220'] | Example value dtype: string\n", "Column name: fiProductClassDesc | Column dtype: object | Example value: ['Track Type Tractor, Dozer - 75.0 to 85.0 Horsepower'] | Example value dtype: string\n", "Column name: state | Column dtype: object | Example value: ['Maryland'] | Example value dtype: string\n", "Column name: ProductGroup | Column dtype: object | Example value: ['WL'] | Example value dtype: string\n", "Column name: ProductGroupDesc | Column dtype: object | Example value: ['Track Type Tractors'] | Example value dtype: string\n"]}], "source": ["# Print column names and example content of columns which contain strings\n", "for label, content in df_tmp.items():\n", "    if pd.api.types.is_string_dtype(content):\n", "        # Check datatype of target column\n", "        column_datatype = df_tmp[label].dtype.name\n", "\n", "        # Get random sample from column values\n", "        example_value = content.sample(1).values\n", "\n", "        # Infer random sample datatype\n", "        example_value_dtype = pd.api.types.infer_dtype(example_value)\n", "        print(f\"Column name: {label} | Column dtype: {column_datatype} | Example value: {example_value} | Example value dtype: {example_value_dtype}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Object Column Analysis\n", "\n", "This code performs a comprehensive analysis of columns with object data types in our DataFrame:\n", "\n", "- Counts and identifies all columns with object data type\n", "- For each object column, it displays:\n", "    - Column name and its data type\n", "    - A random sample value\n", "    - What kind of data we found in the sample?\n", "\n", "This step helps us identify which columns in our data need to be cleaned or adjusted before we can use them in our machine learning model."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Column name: UsageBand | Column dtype: object | Example value: ['Medium'] | Example value dtype: string\n", "Column name: fiModelDesc | Column dtype: object | Example value: ['320CL'] | Example value dtype: string\n", "Column name: fiBaseModel | Column dtype: object | Example value: ['1550'] | Example value dtype: string\n", "Column name: fiSecondaryDesc | Column dtype: object | Example value: [nan] | Example value dtype: empty\n", "Column name: fiModelSeries | Column dtype: object | Example value: ['LC'] | Example value dtype: string\n", "Column name: fiModelDescriptor | Column dtype: object | Example value: [nan] | Example value dtype: empty\n", "Column name: ProductSize | Column dtype: object | Example value: ['Small'] | Example value dtype: string\n", "Column name: fiProductClassDesc | Column dtype: object | Example value: ['Skid Steer Loader - 1351.0 to 1601.0 Lb Operating Capacity'] | Example value dtype: string\n", "Column name: state | Column dtype: object | Example value: ['Texas'] | Example value dtype: string\n", "Column name: ProductGroup | Column dtype: object | Example value: ['TTT'] | Example value dtype: string\n", "Column name: ProductGroupDesc | Column dtype: object | Example value: ['Backhoe Loaders'] | Example value dtype: string\n", "Column name: Drive_System | Column dtype: object | Example value: ['Two Wheel Drive'] | Example value dtype: string\n", "Column name: Enclosure | Column dtype: object | Example value: ['EROPS'] | Example value dtype: string\n", "Column name: <PERSON>s | Column dtype: object | Example value: ['None or Unspecified'] | Example value dtype: string\n", "Column name: Pad_Type | Column dtype: object | Example value: [nan] | Example value dtype: empty\n", "Column name: Ride_Control | Column dtype: object | Example value: [nan] | Example value dtype: empty\n", "Column name: Stick | Column dtype: object | Example value: [nan] | Example value dtype: empty\n", "Column name: Transmission | Column dtype: object | Example value: [nan] | Example value dtype: empty\n", "Column name: Turbocharged | Column dtype: object | Example value: [nan] | Example value dtype: empty\n", "Column name: Blade_Extension | Column dtype: object | Example value: [nan] | Example value dtype: empty\n", "Column name: <PERSON><PERSON>Width | Column dtype: object | Example value: [nan] | Example value dtype: empty\n", "Column name: Enclosure_Type | Column dtype: object | Example value: ['None or Unspecified'] | Example value dtype: string\n", "Column name: Engine_Horsepower | Column dtype: object | Example value: [nan] | Example value dtype: empty\n", "Column name: Hydraulics | Column dtype: object | Example value: ['2 Valve'] | Example value dtype: string\n", "Column name: <PERSON><PERSON><PERSON> | Column dtype: object | Example value: [nan] | Example value dtype: empty\n", "Column name: <PERSON><PERSON><PERSON> | Column dtype: object | Example value: [nan] | Example value dtype: empty\n", "Column name: Scarifier | Column dtype: object | Example value: [nan] | Example value dtype: empty\n", "Column name: Tip_Control | Column dtype: object | Example value: [nan] | Example value dtype: empty\n", "Column name: T<PERSON>_<PERSON>ze | Column dtype: object | Example value: [nan] | Example value dtype: empty\n", "Column name: <PERSON><PERSON><PERSON> | Column dtype: object | Example value: ['None or Unspecified'] | Example value dtype: string\n", "Column name: Coupler_System | Column dtype: object | Example value: [nan] | Example value dtype: empty\n", "Column name: G<PERSON>r_Tracks | Column dtype: object | Example value: [nan] | Example value dtype: empty\n", "Column name: Hydraulics_Flow | Column dtype: object | Example value: ['Standard'] | Example value dtype: string\n", "Column name: Track_Type | Column dtype: object | Example value: [nan] | Example value dtype: empty\n", "Column name: Undercarriage_Pad_Width | Column dtype: object | Example value: ['None or Unspecified'] | Example value dtype: string\n", "Column name: Stick_Length | Column dtype: object | Example value: ['None or Unspecified'] | Example value dtype: string\n", "Column name: Thumb | Column dtype: object | Example value: [nan] | Example value dtype: empty\n", "Column name: Pattern_Changer | Column dtype: object | Example value: ['None or Unspecified'] | Example value dtype: string\n", "Column name: Grouser_Type | Column dtype: object | Example value: [nan] | Example value dtype: empty\n", "Column name: <PERSON><PERSON>_Mounting | Column dtype: object | Example value: [nan] | Example value dtype: empty\n", "Column name: Blade_Type | Column dtype: object | Example value: [nan] | Example value dtype: empty\n", "Column name: Travel_Controls | Column dtype: object | Example value: [nan] | Example value dtype: empty\n", "Column name: Differential_Type | Column dtype: object | Example value: [nan] | Example value dtype: empty\n", "Column name: Steering_Controls | Column dtype: object | Example value: [nan] | Example value dtype: empty\n", "\n", "[INFO] Total number of object type columns: 44\n"]}], "source": ["# Start a count of how many object type columns there are\n", "number_of_object_type_columns = 0\n", "\n", "for label, content in df_tmp.items():\n", "    # Check to see if column is of object type (this will include the string columns)\n", "    if pd.api.types.is_object_dtype(content): \n", "        # Check datatype of target column\n", "        column_datatype = df_tmp[label].dtype.name\n", "\n", "        # Get random sample from column values\n", "        example_value = content.sample(1).values\n", "\n", "        # Infer random sample datatype\n", "        example_value_dtype = pd.api.types.infer_dtype(example_value)\n", "        print(f\"Column name: {label} | Column dtype: {column_datatype} | Example value: {example_value} | Example value dtype: {example_value_dtype}\")\n", "\n", "        number_of_object_type_columns += 1\n", "\n", "print(f\"\\n[INFO] Total number of object type columns: {number_of_object_type_columns}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# **Converting String Data to Numbers in Pandas**\n", "\n", "Turning text data into numbers that our machine learning models can understand.\n", "\n", "### What is Categorical Data?\n", "\n", "Sometimes we have text data (like state names) that we need to convert into numbers. <PERSON><PERSON> helps us do this using something called `\"categories\"`.\n", "\n", "### How It Works\n", "\n", "Think of it like giving each unique text value a number code. For example, if we have states:\n", "\n", "- Alabama becomes 1\n", "- Alaska becomes 2\n", "- Arizona becomes 3\n", "\n", "The best part? The original text values stay the same in our data, but we can easily use their number codes when needed.\n", "\n", "### Getting Started\n", "\n", "To use this feature, we'll convert our text columns to `\"category\"` type using a simple pandas command. "]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["# This will turn all of the object columns into category values\n", "for label, content in df_tmp.items(): \n", "    if pd.api.types.is_object_dtype(content):\n", "        df_tmp[label] = df_tmp[label].astype(\"category\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## DataFrame Information Display\n", "\n", "This code shows basic information about our data using the .info() method. This helps us understand:\n", "\n", "- Shows the total number of entries in the dataset\n", "- Lists all columns and their data types\n", "- Displays memory usage information\n", "- Indicates how many non-null values exist in each column"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 412698 entries, 0 to 412697\n", "Data columns (total 57 columns):\n", " #   Column                    Non-Null Count   Dtype   \n", "---  ------                    --------------   -----   \n", " 0   SalesID                   412698 non-null  int64   \n", " 1   SalePrice                 412698 non-null  float64 \n", " 2   MachineID                 412698 non-null  int64   \n", " 3   ModelID                   412698 non-null  int64   \n", " 4   datasource                412698 non-null  int64   \n", " 5   auctioneerID              392562 non-null  float64 \n", " 6   YearMade                  412698 non-null  int64   \n", " 7   MachineHoursCurrentMeter  147504 non-null  float64 \n", " 8   UsageBand                 73670 non-null   category\n", " 9   fiModelDesc               412698 non-null  category\n", " 10  fiBaseModel               412698 non-null  category\n", " 11  fiSecondaryDesc           271971 non-null  category\n", " 12  fiModelSeries             58667 non-null   category\n", " 13  fiModelDescriptor         74816 non-null   category\n", " 14  ProductSize               196093 non-null  category\n", " 15  fiProductClassDesc        412698 non-null  category\n", " 16  state                     412698 non-null  category\n", " 17  ProductGroup              412698 non-null  category\n", " 18  ProductGroupDesc          412698 non-null  category\n", " 19  Drive_System              107087 non-null  category\n", " 20  Enclosure                 412364 non-null  category\n", " 21  Forks                     197715 non-null  category\n", " 22  Pad_Type                  81096 non-null   category\n", " 23  Ride_Control              152728 non-null  category\n", " 24  Stick                     81096 non-null   category\n", " 25  Transmission              188007 non-null  category\n", " 26  Turbocharged              81096 non-null   category\n", " 27  Blade_Extension           25983 non-null   category\n", " 28  <PERSON><PERSON>Width               25983 non-null   category\n", " 29  Enclosure_Type            25983 non-null   category\n", " 30  Engine_Horsepower         25983 non-null   category\n", " 31  Hydraulics                330133 non-null  category\n", " 32  Pushblock                 25983 non-null   category\n", " 33  <PERSON><PERSON><PERSON>                    106945 non-null  category\n", " 34  Scarifier                 25994 non-null   category\n", " 35  Tip_Control               25983 non-null   category\n", " 36  Tire_Size                 97638 non-null   category\n", " 37  <PERSON><PERSON><PERSON>                   220679 non-null  category\n", " 38  Coupler_System            44974 non-null   category\n", " 39  Grouser_Tracks            44875 non-null   category\n", " 40  Hydraulics_Flow           44875 non-null   category\n", " 41  Track_Type                102193 non-null  category\n", " 42  Undercarriage_Pad_Width   102916 non-null  category\n", " 43  Stick_Length              102261 non-null  category\n", " 44  Thumb                     102332 non-null  category\n", " 45  Pattern_Changer           102261 non-null  category\n", " 46  Grouser_Type              102193 non-null  category\n", " 47  Back<PERSON>_Mounting          80712 non-null   category\n", " 48  Blade_Type                81875 non-null   category\n", " 49  Travel_Controls           81877 non-null   category\n", " 50  Differential_Type         71564 non-null   category\n", " 51  Steering_Controls         71522 non-null   category\n", " 52  saleYear                  412698 non-null  int64   \n", " 53  saleMonth                 412698 non-null  int64   \n", " 54  saleDay                   412698 non-null  int64   \n", " 55  saleDayofweek             412698 non-null  int64   \n", " 56  saleDayofyear             412698 non-null  int64   \n", "dtypes: category(44), float64(3), int64(10)\n", "memory usage: 60.5 MB\n"]}], "source": ["df_tmp.info()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Check Column Data Type\n", "\n", "This code shows us how to look up what kind of data is stored in a specific part (`'state'`) of our data table. It's important to know this because:\n", "\n", "- It helps verify data compatibility before operations\n", "- It ensures proper data handling in analysis\n", "- It helps prevent type-related errors in our code"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["CategoricalDtype(categories=['Alabama', 'Alaska', 'Arizona', 'Arkansas', 'California',\n", "                  'Colorado', 'Connecticut', 'Delaware', 'Florida', 'Georgia',\n", "                  'Hawaii', 'Idaho', 'Illinois', 'Indiana', 'Iowa', 'Kansas',\n", "                  'Kentucky', 'Louisiana', 'Maine', 'Maryland',\n", "                  'Massachusetts', 'Michigan', 'Minnesota', 'Mississippi',\n", "                  'Missouri', 'Montana', 'Nebraska', 'Nevada', 'New Hampshire',\n", "                  'New Jersey', 'New Mexico', 'New York', 'North Carolina',\n", "                  'North Dakota', 'Ohio', 'Oklahoma', 'Oregon', 'Pennsylvania',\n", "                  'Puerto Rico', 'Rhode Island', 'South Carolina',\n", "                  'South Dakota', 'Tennessee', 'Texas', 'Unspecified', 'Utah',\n", "                  'Vermont', 'Virginia', 'Washington', 'Washington DC',\n", "                  'West Virginia', 'Wisconsin', 'Wyoming'],\n", ", ordered=False, categories_dtype=object)"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# Check the datatype of a single column\n", "df_tmp.state.dtype"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Get Category Names\n", "\n", "This code shows us all the different values that exist in our data column. This helps us:\n", "\n", "- Viewing all possible values in a categorical variable\n", "- Verifying the category encoding worked correctly\n", "- Understanding the range of values in our categorical data"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['Alabama', 'Alaska', 'Arizona', 'Arkansas', 'California', 'Colorado',\n", "       'Connecticut', 'Delaware', 'Florida', 'Georgia', 'Hawaii', 'Idaho',\n", "       'Illinois', 'Indiana', 'Iowa', 'Kansas', 'Kentucky', 'Louisiana',\n", "       'Maine', 'Maryland', 'Massachusetts', 'Michigan', 'Minnesota',\n", "       'Mississippi', 'Missouri', 'Montana', 'Nebraska', 'Nevada',\n", "       'New Hampshire', 'New Jersey', 'New Mexico', 'New York',\n", "       'North Carolina', 'North Dakota', 'Ohio', 'Oklahoma', 'Oregon',\n", "       'Pennsylvania', 'Puerto Rico', 'Rhode Island', 'South Carolina',\n", "       'South Dakota', 'Tennessee', 'Texas', 'Unspecified', 'Utah', 'Vermont',\n", "       'Virginia', 'Washington', 'Washington DC', 'West Virginia', 'Wisconsin',\n", "       'Wyoming'],\n", "      dtype='object')"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["# Get the category names of a given column\n", "df_tmp.state.cat.categories"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## View Category Codes\n", "\n", "This code shows how the computer converts text categories (like state names) into numbers. This helps us:\n", "\n", "- Understanding how categorical data is encoded internally\n", "- Checking if the numbers match up correctly with their text labels\n", "- Debugging category-related operations in the data preprocessing pipeline"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["0          0\n", "1         32\n", "2         31\n", "3         43\n", "4         31\n", "          ..\n", "412693    43\n", "412694     8\n", "412695     8\n", "412696    43\n", "412697     8\n", "Length: 412698, dtype: int8"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["# Inspect the category codes\n", "df_tmp.state.cat.codes"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Get State Name from Category Number\n", "\n", "This code shows how to look up a state's name when we have its number code. This is helpful when:\n", "\n", "- Converting encoded numerical values back to their original text labels\n", "- Verifying the category encoding system is working correctly\n", "- Debugging and data validation when working with categorical variables"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[INFO] Target state category number 39 maps to: Rhode Island\n"]}], "source": ["# Get example string using category number\n", "target_state_cat_number = 39\n", "target_state_cat_value = df_tmp.state.cat.categories[target_state_cat_number] \n", "print(f\"[INFO] Target state category number {target_state_cat_number} maps to: {target_state_cat_value}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Save Preprocessed Data to CSV - Checkpoint 1\n", "\n", "This code takes our processed data and saves it as a CSV file.\n", "\n", "- Attempts to save the DataFrame (df_tmp) to a specified file path\n", "- Uses error handling (try/except) to catch and report any potential issues during saving\n", "- Prints a success message when the file is saved correctly"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SUCCESSFULLY SAVED! The data file 'TrainAndValid_object_values_as_categories.csv' has been successfully saved in data/processed.\n"]}], "source": ["import pandas as pd\n", "\n", "# Assuming df_tmp is your preprocessed DataFrame\n", "try:\n", "    df_tmp.to_csv(\"C:/Users/<USER>/Dropbox/1 PROJECT/VS Code Project Respository/About-BulldozerPriceGenius-_BPG-_v2/data/processed/TrainAndValid_object_values_as_categories.csv\",\n", "                  index=False)\n", "    print(\"SUCCESSFULLY SAVED! The data file 'TrainAndValid_object_values_as_categories.csv' has been successfully saved in data/processed.\")\n", "except Exception as e:\n", "    print(f\"An error occurred while saving the preprocessed data: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# **Conclusions and Next Steps**\n", "\n", "## Conclusions\n", "\n", "* The exploratory data analysis (EDA) provided valuable insights into the dataset, including the distribution of key variables, identification of missing values, and potential correlations between features.\n", "* Key findings include:\n", "  * The target variable (e.g., bulldozer prices) shows a right-skewed distribution.\n", "  * Certain features have a high percentage of missing values and may require imputation or removal.\n", "  * There are strong correlations between some features, which could be useful for feature engineering.\n", "\n", "## Next Steps\n", "- `04_data_preprocessing.ipynb`: This notebook will handle converting data types, addressing missing values, and preparing the data for modeling.\n"]}], "metadata": {"accelerator": "GPU", "colab": {"name": "Data Practitioner Jupyter Notebook.ipynb", "provenance": [], "toc_visible": true}, "kernelspec": {"display_name": "myenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}, "orig_nbformat": 2}, "nbformat": 4, "nbformat_minor": 2}