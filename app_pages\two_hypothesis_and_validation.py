import streamlit as st
import os


def hypothesis_and_validation_body():
    """
    Renders the Hypothesis & Validation page content
    Displays performance metrics and validation results for the ML model
    Returns: None
    """

    # --------------------------------------------------------------------------
    # 1. PAGE SETUP
    # --------------------------------------------------------------------------
    st.subheader("*Hypothesis and Validation*")

    # Overview section explaining page purpose
    st.markdown(
        """
        The BulldozerPriceGenius app helps you see how accurate our price predictions are.
        This page shows you how well our machine learning model predicts bulldozer auction prices.
        The project has one main objective based on the project business requirements:
        """
    )

    # Main objective callout
    st.success(
        """
        - **Objective 1**: A user can evaluate model performance metrics to ensure our price
        predictions are reliable and accurate (*Business Requirement 2*)
        """
    )

    # --------------------------------------------------------------------------
    # 2. NAVIGATION
    # --------------------------------------------------------------------------
    st.markdown(
        """
        - [What We Are Testing](#what-we-are-testing)
        - [How We Validate](#how-we-validate)
        - [Validation 1: Price Accuracy](#validation-1-price-accuracy)
        - [Validation 2: Feature Significance](#validation-2-feature-significance)
        - [Validation 3: Model Performance](#validation-3-model-performance)
        """
    )
    st.write("---")

    # --------------------------------------------------------------------------
    # 3. HYPOTHESES DEFINITION
    # --------------------------------------------------------------------------
    st.subheader("What We Are Testing")
    st.markdown(
        """
        This section will contain the hypothesis for the Bulldozer Price Genius project.
        1. **Price Accuracy**: We believe that our model can predict prices within an acceptable
        margin of error, with an RMSLE score less than 1.0
        2. **Feature Significance**: We expect these five bulldozer features (year made, product size, sale year, model description, and model ID) to have a stronger influence on price predictions
        3. **Model Performance**: We predict that different machine learning models will show varying levels of prediction accuracy, but both ideal and fast models will maintain acceptable performance below the target `RMSLE` threshold
        """
    )
    st.write("---")

    # --------------------------------------------------------------------------
    # 4. VALIDATION METHODOLOGY
    # --------------------------------------------------------------------------
    st.subheader("How We Validate")
    st.markdown(
        """
        - `RMSLE scores` show how close our predictions are to actual prices
        - `Model comparison` helps us choose the best performing solution

        All validation metrics directly support:
        """
    )
    st.info(
        """
        **Business Requirement 2** - The client requires a machine learning system that can accurately predict bulldozer prices based on historical auction data with a Root Mean Squared Log Error (RMSLE) score of below 1.0
        """
    )
    st.write("---")

    # --------------------------------------------------------------------------
    # 5. VALIDATION RESULTS
    # --------------------------------------------------------------------------
    # 5.1 Price Accuracy Validation
    st.header("Validation 1: Price Accuracy")
    st.success(
        """
        **Hypothesis 1**: We believe that our model can predict prices within an acceptable
        margin of error, with an RMSLE score less than `1.0`
        """
    )
    st.image("results/sale_price.webp")

    # Display comparison metrics
    st.subheader("Prediction vs Reality")
    col1, col2 = st.columns(2)
    with col1:
        st.metric("Model Prediction", f"${55495.68:,.2f}")
    with col2:
        st.metric("Actual Price", f"${72600:,.2f}")

    # Display error metrics
    st.subheader("Performance Metrics")
    col1, col2 = st.columns(2)
    with col1:
        st.metric("Mean Absolute Error (MAE)", f"${17104:,.2f}")
    with col2:
        st.metric("RMSLE Score", "0.27")

    # Analysis summary
    st.subheader("Analysis")
    st.write(
        """
        - RMSLE score of 0.27 indicates reasonable model performance
        - Model provides valuable pricing guidance
        - Some room for improvement exists
        """
    )

    # Validation conclusions
    st.subheader("Conclusion")
    st.write(
        """
        Yes, our hypothesis was validated. Our target **RMSLE** score was below `1.0`, and we achieved `0.27` — significantly exceeding our expectations. While we've met our goal, we can further improve by reducing the **Mean Absolute Error (MAE)** from the current `$17,104` average error, though we have already achieved our secondary success metric of staying below `$20,000` error price limit. Users can confidently rely on the model's price estimates.

        **What does this mean?**
        - Our predictions are more accurate than expected
        - Users can trust our model for pricing guidance
        - The system is ready for real-world use
        """
    )
    st.write("---")

    # 5.2 Feature Importance Validation
    st.header("Validation 2: Feature Significance")
    st.success(
        """
        **Hypothesis 2**: We expect these five bulldozer features (year made, product size, sale year, model description, and model ID) to have a stronger influence on price predictions
        """
    )
    st.image("results/feature_importance.webp")

    # Feature analysis
    st.subheader("Analysis of Top Features")
    st.write(
        """
        **Primary Features:**
        - **Year Made** (`19.9%`): The most significant factor, with newer bulldozers commanding higher prices
        - **Product Size** (`15.5%`): Second most important, larger machines typically cost more

        **Secondary Features:**
        - **Sale Year** (`7.7%`): Reflects market conditions at time of sale
        - **Model Description** (`5.7%`): Specific model features impact pricing
        - **Model ID** (`5.6%`): Different models have varying base prices
        """
    )

    # Feature significance summary
    st.subheader("Conclusion")
    st.write(
        """
        Our analysis validates the hypothesis regarding feature importance: bulldozer **age** and **size** together influence approximately `35%` of the price predictions, confirming these as the most significant features
        """
    )
    st.write("---")

    # 5.3 Model Performance Validation
    st.header("Validation 3: Model Performance")
    st.success(
        """
        **Hypothesis 3**: We predict that different machine learning models will show varying levels of prediction accuracy, but both ideal and fast models will maintain acceptable performance below the target RMSLE threshold
        """
    )
    st.image("results/model_comparison.webp")

    # Performance summary
    st.subheader("Conclusion")
    st.write(
        """
        Yes, Hypothesis 3 is validated. Our analysis shows distinct variations in model performance, with all models maintaining acceptable RMSLE scores:

        - The **ideal model** achieves the best performance with a `Valid RMSLE` of `0.313` and `Valid R²` of `0.836`
        - The **fast model** performs similarly well, with a `Valid RMSLE` of `0.318` and `Valid R²` of `0.830`
        - The **default and random search models** achieve acceptable `RMSLE scores` of `0.358` and `0.349`, respectively

        **Key findings**:
        - All models maintain `RMSLE scores` well below our target of `1.0`, demonstrating reliable performance
        - The **ideal and fast models** show exceptional results with negligible differences, making both viable choices based on speed requirements
        - `High R²` values, particularly in the **ideal and fast models**, demonstrate strong predictive accuracy
        """
    )
    st.write("---")


# --------------------------------------------------------------------------
# MAIN EXECUTION
# --------------------------------------------------------------------------
if __name__ == "__main__":
    hypothesis_and_validation_body()
